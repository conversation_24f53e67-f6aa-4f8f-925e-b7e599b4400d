import { Badge, type BadgeConfig } from "@components/common";
import { cn } from "@utils/cn";

export type TaskStatusType = "completed" | "onboarding";

interface TaskStatusBadgeConfig extends BadgeConfig {
  type: TaskStatusType;
}

const TASK_STATUS_BADGE_CONFIG: Record<TaskStatusType, TaskStatusBadgeConfig> = {
  completed: {
    containerClasses: "bg-success-content !text-label-xs",
    icon: <div className="size-2 rounded-full bg-primary" />,
    iconAlt: "completed",
    textClasses: "text-primary",
    type: "completed",
  },
  onboarding: {
    containerClasses: "bg-rose-50 !text-label-xs",
    icon: <div className="size-2 rounded-full bg-error" />,
    iconAlt: "onboarding",
    textClasses: "text-secondary",
    type: "onboarding",
  },
};

interface TaskStatusBadgeProps {
  type?: TaskStatusType;
  className?: string;
  label?: string;
}

export const TaskStatusBadge = ({ type, className, label }: TaskStatusBadgeProps) => {
  return (
    <Badge
      type={type}
      className={cn("border-none", className)}
      config={{ ...TASK_STATUS_BADGE_CONFIG, ...(label ? { label } : {}) }}
    />
  );
};

export const TASK_STATUS_OPTIONS = (["completed", "onboarding"] as TaskStatusType[]).map(
  (type) => ({
    label: <TaskStatusBadge type={type} />,
    value: type,
  }),
);
