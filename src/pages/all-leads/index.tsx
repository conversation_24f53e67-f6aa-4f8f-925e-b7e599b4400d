import {
  ChannelBadge,
  type ChanneType,
  FollowUpBadge,
  type FollowUpStatusType,
  OpportunityBadge,
  type OpportunityType,
  ServiceBadge,
  type ServiceType,
  UserStatusBadge,
  type UserStatusType,
} from "@components/badge";
import { Container, Progress, Select } from "@components/common";
import { RouthPath } from "@enums/route-path";
import { CaretRightIcon } from "@phosphor-icons/react";
import { useNavigate } from "@tanstack/react-router";
import { cn } from "@utils/cn";
import { useTranslation } from "react-i18next";
import type { LeadProps } from "./interface";
import { leads } from "./mock";

interface PageTitleBarProps {
  leadCount: number;
}

const PageTitleBar = ({ leadCount }: PageTitleBarProps) => {
  const { t } = useTranslation();
  return (
    <div className="my-4 flex w-full justify-between">
      <div className="flex h-fit items-center gap-2">
        <h3>{t("allLeads.allLead")}</h3>
        <div className="size-6 place-content-center rounded-full bg-secondary">
          <h6 className="place-self-center text-white">{leadCount}</h6>
        </div>
      </div>
      <Select className="w-full" size="sm" />
    </div>
  );
};

const TabalTitleBar = () => {
  const { t } = useTranslation();
  return (
    <div
      className={cn(
        "grid grid-cols-[2%_10%_10%_16%_1fr_10%_10%_10%_10%_4%]",
        "place-items-center rounded-lg bg-primary p-4 text-white",
      )}
    >
      <div />
      <h6>{t("allLeads.opportunity")}</h6>
      <h6>{t("allLeads.contactChannel")}</h6>
      <h6>{t("allLeads.name")}</h6>
      <h6>{t("allLeads.servicesOfInterest")}</h6>
      <h6>{t("allLeads.followUpStatus")}</h6>
      <h6>{t("allLeads.followUpDate")}</h6>
      <h6>{t("allLeads.tasks")}</h6>
      <h6>{t("allLeads.userStatus")}</h6>
      <div />
    </div>
  );
};

const LeadRow = ({ lead, index }: { lead: LeadProps; index: number }) => {
  const navigate = useNavigate();
  return (
    <button
      type="button"
      className={cn(
        "grid h-12 cursor-pointer grid-cols-[3%_10%_10%_15%_1fr_10%_10%_10%_10%_5%]",
        "place-items-center border-base-300 border-b",
        "hover:bg-success-content/50",
      )}
      onClick={() => navigate({ to: `${RouthPath.PROFILE}/${lead.name}` })}
    >
      <h6>{index + 1}.</h6>
      <OpportunityBadge type={lead.opportunity as OpportunityType} />
      <ChannelBadge type={lead.contactChannel as ChanneType} />
      <p className="text-body-sm">{lead.name}</p>
      <ServiceBadge type={lead.servicesOfInterest as ServiceType} />
      <FollowUpBadge type={lead.followUpStatus as FollowUpStatusType} />
      <p className="text-body-sm">{lead.followUpDate}</p>
      <Progress tasks={lead.tasks} />
      <UserStatusBadge type={lead.userStatus as UserStatusType} />
      <CaretRightIcon size={24} weight="bold" />
    </button>
  );
};

export function AllLeads() {
  return (
    <Container>
      <PageTitleBar leadCount={leads.length} />
      <div className="border-container">
        <TabalTitleBar />
        {leads.map((lead, i) => (
          <LeadRow key={lead.name} lead={lead} index={i} />
        ))}
      </div>
    </Container>
  );
}
