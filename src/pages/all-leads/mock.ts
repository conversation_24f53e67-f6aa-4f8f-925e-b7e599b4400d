import type { LeadProps } from "./interface";

const today = new Intl.DateTimeFormat("en-GB", {
  day: "2-digit",
  month: "2-digit",
  year: "numeric",
}).format(new Date());

export const leads: LeadProps[] = [
  {
    contactChannel: "line",
    followUpDate: today,
    followUpStatus: "contacted",
    name: "<PERSON>",
    opportunity: "hot",
    servicesOfInterest: "botox",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
    userStatus: "draft",
  },
  {
    contactChannel: "instagram",
    followUpDate: today,
    followUpStatus: "interested",
    name: "<PERSON>",
    opportunity: "hot",
    servicesOfInterest: "hifu",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
    userStatus: "active",
  },
  {
    contactChannel: "tiktok",
    followUpDate: today,
    followUpStatus: "pending",
    name: "<PERSON>",
    opportunity: "warm",
    servicesOfInterest: "thermage",
    tasks: [
      { completed: true, task: "Call back" },
      { completed: true, task: "Schedule appointment" },
      { completed: true, task: "Follow up" },
    ],
    userStatus: "completed",
  },
  {
    contactChannel: "facebook",
    followUpDate: today,
    followUpStatus: "not_interested",
    name: "John Doreamon",
    opportunity: "cold",
    servicesOfInterest: "juvelook",
    tasks: [
      { completed: false, task: "Call back" },
      { completed: false, task: "Schedule appointment" },
      { completed: false, task: "Follow up" },
    ],
    userStatus: "suspended",
  },
];
